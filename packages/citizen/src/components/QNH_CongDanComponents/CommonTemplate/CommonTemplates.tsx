import React from 'react';
import {View} from 'react-native';
import {CommonProcedureItem} from './CommonProcedureItem';
import {CardComponent} from '../CardComponent/CardComponent';
import {LayoutComponent} from '../LayoutComponent/LayoutComponent';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '../../../navigation/MainNavigator';
import {IChoseCommonProcedure, useCommonProcedure} from '../../../stores';
import {
  ICommonProcedureLoading,
  UseRequesterBizTemplates,
} from '../../../useQueryState';
import {AnyObj} from '@ac-mobile/common';
import {ColorPickerIcon} from './ColorPickerIcon';
import {generateDataLoading} from '../../../utils/fn';

export const CommonTemplates = React.memo(
  React.forwardRef((props, ref) => {
    // COMMENT: Store
    const {getProcedureItem} = useCommonProcedure();

    const nav = useNavigation<NavigationProp<MainStackParamList>>();

    const [data, setData] = React.useState<ICommonProcedureLoading[]>([]);

    const params = {
      page: 1,
      pageSize: 6,
      orderBy: 'createdAt',
      orderDir: 'desc',
      status: 'ACTIVE',
      tags: 'DVCQNMOBILE',
    };

    const {
      data: templateSelected,
      isLoading: isLoadingTemplate,
      refetch,
      // isError: errorTemplates,
    } = UseRequesterBizTemplates({params});

    // Expose refetch to parent via ref
    React.useImperativeHandle(ref, () => ({
      refetchTemplates: refetch,
    }));

    React.useEffect(() => {
      if (isLoadingTemplate) {
        const payload = {
          id: '0',
          name: '',
          statusLoading: true,
        } as ICommonProcedureLoading;
        const responseLoading = generateDataLoading({count: 6, payload});
        setData(responseLoading);
      } else if (!isLoadingTemplate && templateSelected?.data?.data?.items) {
        const responseTemplate = templateSelected?.data?.data?.items.map(
          (item: AnyObj) => ({
            id: item?.id || 0,
            name: item?.name || '',
            statusLoading: false,
          }),
        ) as ICommonProcedureLoading[];
        setData(responseTemplate);
      }
    }, [isLoadingTemplate, templateSelected?.data?.data?.items]);

    const handleGo = async ({id, name}: {id: string; name: string}) => {
      const procedureItem: IChoseCommonProcedure = {
        procedureId: id,
        procedureName: name,
      };
      await getProcedureItem(procedureItem);
      nav.navigate('CommonProcedureDetail');
    };

    const handleOnPress = () => {
      nav.navigate('CommonProcedure');
    };

    return (
      <View>
        <CardComponent title="Thủ tục " onPress={handleOnPress}>
          <LayoutComponent type="grid" columns={1}>
            {data.map((item: ICommonProcedureLoading, index: number) => (
              <CommonProcedureItem
                key={index}
                title={item.name}
                direction="horizontal"
                id={item.id}
                isLoading={item.statusLoading}
                onPress={(id: string, name: string) => handleGo({id, name})}
                icon={<ColorPickerIcon paint={['#919EAB', '#919EAB']} />}
              />
            ))}
          </LayoutComponent>
        </CardComponent>
      </View>
    );
  }),
);
