import React, {useState, useEffect, useMemo, useCallback} from 'react';
import {View, StyleSheet, BackHandler} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {NavigationProp} from '@react-navigation/native';
import {MainStackParamList} from '../../../../navigation/MainNavigator';
import {CCAppBar} from '../../../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {UseRequesterBizTemplates} from '../../../../hooks/UseRequesterBizTemplates';
import {useCommonProcedure, IChoseCommonProcedure} from '../../../../stores';
import {normalizeVietnameseText} from '../../../../utils/textUtils';
import {useTheme} from 'react-native-paper';
import {AnyObj} from '@ac-mobile/common';

// Import components
import LoadingState from '../DrawNumber/components/LoadingState';
import ErrorState from '../DrawNumber/components/ErrorState';
import TemplateList from './components/TemplateList';

// Template interface
interface Template {
  id: string;
  name: string;
}

export const TemplatesScreen = React.memo(() => {
  const theme = useTheme();
  const navigation = useNavigation<NavigationProp<MainStackParamList>>();
  const {getProcedureItem} = useCommonProcedure();

  const [templates, setTemplates] = useState<Template[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
    null,
  );
  const [hasReturned] = useState(false);

  const handleBackPress = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  // Handle hardware back button (Android)
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        handleBackPress();
        return true; // Prevent default behavior
      };

      const subscription = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress,
      );

      return () => subscription.remove();
    }, [handleBackPress]),
  );

  const filteredTemplates = useMemo(() => {
    if (!searchQuery.trim()) {
      return templates;
    }

    const normalizedQuery = normalizeVietnameseText(searchQuery.toLowerCase());
    return templates.filter(template => {
      const normalizedName = normalizeVietnameseText(
        template.name?.toLowerCase() || '',
      );

      return normalizedName.includes(normalizedQuery);
    });
  }, [templates, searchQuery]);

  const params = useMemo(
    () => ({
      page: 1,
      pageSize: 21,
      orderBy: 'createdAt',
      orderDir: 'desc',
      status: 'ACTIVE',
      tags: 'DVCQNMOBILE',
      ...(searchQuery.trim() && {keyword: searchQuery.trim()}),
    }),
    [searchQuery],
  );

  const {
    data: templateData,
    isLoading,
    error,
    refetch,
  } = UseRequesterBizTemplates({params, textSearchProcedureItem: searchQuery});

  useEffect(() => {
    if (!isLoading && templateData?.data?.data?.items) {
      const formattedTemplates = templateData.data.data.items.map(
        (item: AnyObj) => ({
          id: item?.id || '',
          name: item?.name || '',
        }),
      ) as Template[];
      setTemplates(formattedTemplates);
    }
  }, [isLoading, templateData?.data?.data?.items]);

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
    const procedureItem: IChoseCommonProcedure = {
      procedureId: template.id,
      procedureName: template.name,
    };
    getProcedureItem(procedureItem);
    navigation.navigate('CommonProcedureDetail');
  };

  const handleRetry = () => {
    refetch();
  };

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: theme.colors.background}]}
      edges={['bottom']}>
      <CCAppBar
        isBack={true}
        showSearch={true}
        searchPlaceholder="Tìm kiếm thủ tục..."
        searchValue={searchQuery}
        onSearchChange={setSearchQuery}
      />
      <View
        style={[styles.content, {backgroundColor: theme.colors.background}]}>
        {isLoading ? (
          <LoadingState message="Đang tải danh sách thủ tục..." />
        ) : error ? (
          <ErrorState
            error={
              (error as any)?.message ||
              'Đã có lỗi xảy ra khi tải danh sách thủ tục'
            }
            onRetry={handleRetry}
          />
        ) : (
          <TemplateList
            templates={filteredTemplates}
            selectedTemplate={selectedTemplate}
            hasReturned={hasReturned}
            onSelectTemplate={handleTemplateSelect}
            searchQuery={searchQuery}
          />
        )}
      </View>
    </SafeAreaView>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 12,
    paddingTop: 12,
  },
});
