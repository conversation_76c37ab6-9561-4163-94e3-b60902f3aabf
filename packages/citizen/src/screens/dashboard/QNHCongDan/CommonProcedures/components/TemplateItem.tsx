import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useTheme} from 'react-native-paper';
import {textStyles} from '../../../../../styles/QNH_textStyle';

// Template interface
interface Template {
  id: string;
  name: string;
}

interface TemplateItemProps {
  template: Template;
  isSelected: boolean;
  hasReturned: boolean;
  onSelect: (template: Template) => void;
}

const TemplateItem: React.FC<TemplateItemProps> = ({
  template,
  isSelected,
  hasReturned,
  onSelect,
}) => {
  const theme = useTheme();

  const handlePress = () => {
    onSelect(template);
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      marginBottom: 8,
      borderWidth: isSelected ? 2 : 1,
      borderColor: isSelected ? theme.colors.primary : theme.colors.outline,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    touchable: {
      padding: 16,
    },
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    leftContent: {
      flex: 1,
      marginRight: 12,
    },
    templateName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 4,
      lineHeight: 22,
    },
    templateId: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      opacity: 0.7,
    },
    rightContent: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    selectedIndicator: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    selectedText: {
      color: theme.colors.onPrimary,
      fontSize: 12,
      fontWeight: 'bold',
    },
    unselectedIndicator: {
      width: 24,
      height: 24,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: theme.colors.outline,
    },
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.touchable} onPress={handlePress}>
        <View style={styles.content}>
          <View style={styles.leftContent}>
            <Text className={`${textStyles.h6}`} style={styles.templateName}>
              {template.name}
            </Text>
            <Text className={`${textStyles.caption}`} style={styles.templateId}>
              ID: {template.id}
            </Text>
          </View>
          <View style={styles.rightContent}>
            {isSelected ? (
              <View style={styles.selectedIndicator}>
                <Text style={styles.selectedText}>✓</Text>
              </View>
            ) : (
              <View style={styles.unselectedIndicator} />
            )}
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default TemplateItem;
