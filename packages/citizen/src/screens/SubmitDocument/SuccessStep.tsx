import React from 'react';
import {View, StyleSheet, ScrollView} from 'react-native';
import {Text, Button, Divider, useTheme} from 'react-native-paper';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  useNavigation,
  NavigationProp,
  useRoute,
  RouteProp,
} from '@react-navigation/native';
import {MainStackParamList} from '../../navigation/MainNavigator';
import {useCommonProcedure} from '../../stores';
import {commonStyle} from './commonStyle';

// Props expected: soBienNhan, nopHoSoSoTienThanhToan, nopHoSoSoTienCanThanhToanSau, tongTien, title, isPayment
// These can be passed via navigation params or from zustand store

type SuccessStepRouteProp = RouteProp<MainStackParamList, 'SuccessStep'>;

export const SuccessStep = () => {
  const theme = useTheme();
  const nav = useNavigation<NavigationProp<MainStackParamList>>();
  const route = useRoute<SuccessStepRouteProp>();
  // Try to get from params, fallback to zustand if needed
  const {
    soBienNhan,
    nopHoSoSoTienThanhToan,
    nopHoSoSoTienCanThanhToanSau,
    tongTien,
    title,
    isPayment = false,
  } = route.params || {};
  const choseCommonProcedureItem = useCommonProcedure(
    state => state.choseCommonProcedureItem,
  );
  // fallback title
  const displayTitle =
    title || choseCommonProcedureItem?.procedureName || 'Dịch vụ công';

  // Clear state and go home logic
  const handleGoHome = () => {
    // Reset navigation stack to go to home screen
    nav.reset({
      index: 0,
      routes: [{name: 'Dashboard', params: {screen: 'HomeNavigator'}}],
    });
  };

  return (
    <View
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <CCAppBar label="Kết quả" isBack={true} onBackPressed={handleGoHome} />
      <ScrollView contentContainerStyle={styles.scrollContentContainer}>
        <View style={styles.centered}>
          <View style={styles.avatarWrap}>
            <View
              style={[
                styles.circleAvatar,
                {backgroundColor: theme.colors.primaryContainer + '22'},
              ]}>
              <Icon
                name="check"
                size={40}
                color={theme.colors.onPrimaryContainer}
              />
            </View>
          </View>
          <Text style={styles.successTitle}>
            {isPayment
              ? 'Thanh toán và gửi hồ sơ thành công'
              : 'Gửi hồ sơ thành công'}
          </Text>
          <View style={styles.infoBox}>
            <Text style={styles.infoBoxTitle}>Thông tin dịch vụ</Text>
            <Divider style={styles.divider} />
            <View style={styles.rowBetween}>
              <Text style={styles.infoLabel}>{displayTitle}</Text>
            </View>
            {!!soBienNhan && (
              <Text style={styles.infoValue}>Mã hồ sơ: {soBienNhan}</Text>
            )}
            {!!nopHoSoSoTienThanhToan && (
              <>
                <Divider style={styles.divider} />
                <View style={styles.rowBetween}>
                  <Text style={styles.infoLabel}>
                    Thanh toán thành công số tiền:
                  </Text>
                  <Text style={styles.infoValue}>
                    {nopHoSoSoTienThanhToan} VNĐ
                  </Text>
                </View>
              </>
            )}
            {!!nopHoSoSoTienCanThanhToanSau && (
              <>
                <Divider style={styles.divider} />
                <View style={styles.rowBetween}>
                  <Text style={styles.infoLabel}>Cần nộp sau số tiền:</Text>
                  <Text style={styles.infoValue}>
                    {nopHoSoSoTienCanThanhToanSau} VNĐ
                  </Text>
                </View>
              </>
            )}
            <Divider style={styles.divider} />
            {!!tongTien && (
              <View style={styles.rowBetween}>
                <Text style={styles.infoLabel}>Tổng chi phí:</Text>
                <Text style={styles.infoValue}>{tongTien} VNĐ</Text>
              </View>
            )}
          </View>
        </View>
      </ScrollView>
      <View style={commonStyle.footer}>
        <Button
          style={commonStyle.footerButton}
          mode="contained"
          onPress={handleGoHome}>
          Về trang chủ
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContentContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 16,
    paddingBottom: 120,
  },
  centered: {
    alignItems: 'center',
    marginTop: 40,
  },
  avatarWrap: {
    marginBottom: 20,
  },
  circleAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
  },
  infoBox: {
    width: '100%',

    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#eee',
    padding: 16,
    marginBottom: 16,
  },
  infoBoxTitle: {
    fontWeight: 'bold',
    marginBottom: 12,
  },
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 15,

    flex: 1,
    flexWrap: 'wrap',
  },
  infoValue: {
    fontWeight: 'bold',

    fontSize: 15,
    marginLeft: 8,
  },
  divider: {
    marginVertical: 8,
  },
  footer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    padding: 16,

    borderTopWidth: 1,
  },
  button: {
    borderRadius: 12,
  },
});

export default SuccessStep;
