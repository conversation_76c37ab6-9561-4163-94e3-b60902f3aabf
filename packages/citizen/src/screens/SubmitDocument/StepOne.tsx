import React, {memo, useEffect, useRef, useState} from 'react';

import {StyleSheet, View} from 'react-native';
import {useSubmitFlow} from '../../hooks/useSubmitFlow';
import {IOptionMenuItem, StepMenuVertical} from '../../components/AcStepMenu';
import {
  InformationUserScreenRef,
  InformationUserScreen,
} from '../dashboard/QNHCongDan/CommonProcedureSubmit/InformationUserScreen';
import {Button, useTheme} from 'react-native-paper';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {useNavigation, NavigationProp} from '@react-navigation/native';
import {MainStackParamList} from '../../navigation/MainNavigator';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {commonStyle} from './commonStyle';

const optionSteps: IOptionMenuItem[] = [
  {id: 1, name: '<PERSON><PERSON> b<PERSON><PERSON> thông tin người yêu cầu'},
  {id: 2, name: '<PERSON><PERSON> khai biểu mẫu điện tử'},
  {id: 3, name: 'Đính kèm giấy tờ'},
  {id: 4, name: 'Nhận kết quả & thanh toán'},
];

export const StepOne = memo(() => {
  const theme = useTheme();
  const [title] = useState<string>('Nộp hồ sơ');
  const nav = useNavigation<NavigationProp<MainStackParamList>>();
  const infoUserRef = useRef<InformationUserScreenRef>(null);
  const [isFormValid, setIsFormValid] = useState(false);
  const [isFormFilled, setIsFormFilled] = useState(false);
  const [missingFields, setMissingFields] = useState<string[]>([]);
  const {setStepOneFormData} = useSubmitFlow();

  useEffect(() => {
    const interval = setInterval(() => {
      if (
        infoUserRef.current &&
        typeof infoUserRef.current.isValid === 'function'
      ) {
        const valid = infoUserRef.current.isValid();
        setIsFormValid(valid);
        const formValues = infoUserRef.current?.methods?.getValues?.();
        const requiredFields = [
          'FullName',
          'Nationality',
          'CitizenCode',
          'Sex',
          'Nation',
          'Religion',
          'DateOfBirth',
          'DateOfProvision',
          'PlaceOfOrigin',
          'PlaceOfResidence',
          'DocumentType',
          'PlaceOfIssues',
        ];
        const missing = requiredFields.filter(
          key =>
            !formValues || formValues[key] === null || formValues[key] === '',
        );
        setMissingFields(missing);
        setIsFormFilled(missing.length === 0);
      }
    }, 500);
    return () => clearInterval(interval);
  }, []);

  // Handler for both AppBar and footer next button
  const handleNext = () => {
    if (!infoUserRef.current) {
      return;
    }
    const {methods, focusFirstErrorField} = infoUserRef.current;
    methods.handleSubmit(
      (data: any) => {
        try {
          setStepOneFormData(data);
        } catch (error) {
          console.error('Error setting step one form data:', error);
        }
        nav.navigate('StepRoleChoose');
      },
      (errors: any) => {
        focusFirstErrorField(errors);
      },
    )();
  };

  return (
    <View style={[styles.container, {backgroundColor: theme.colors.surface}]}>
      <CCAppBar
        label={title}
        isBack={true}
        iconsAction={[
          {
            icon: 'arrow-right',
            onPress: handleNext,
            // Only enable if form is valid and filled
            disabled: !isFormValid || !isFormFilled || missingFields.length > 0,
          },
        ]}
      />
      <StepMenuVertical currentStep={1} options={optionSteps} />

      {/* Increased offset */}
      <KeyboardAwareScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        keyboardDismissMode="interactive"
        scrollEventThrottle={16}
        enableOnAndroid
        extraScrollHeight={80}>
        <InformationUserScreen ref={infoUserRef} />
      </KeyboardAwareScrollView>
      <View
        style={[commonStyle.footer, {backgroundColor: theme.colors.surface}]}>
        <Button
          style={commonStyle.footerButton}
          mode="contained"
          disabled={!isFormValid || !isFormFilled || missingFields.length > 0}
          onPress={handleNext}>
          Tiếp theo
        </Button>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 90, // Enough space for the footer button
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
  },
});
