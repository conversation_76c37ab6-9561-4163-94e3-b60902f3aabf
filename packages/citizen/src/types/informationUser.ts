export type submitScreenRef = {
  submit: () => void;
};

export enum EBtsInformationUserOptions {
  SEX = 'Sex',
  NATIONALITY = 'Nationality',
  NATION = 'Nation',
  RELIGION = 'Religion',
  DOCUMENT_TYPE = 'DocumentType',
  PLACE_OF_ISSUES = 'PlaceOfIssues',
  DOITUONG = 'DoiTuong',
  DOITUONGTHUCHIEN = 'DoiTuongThucHien',
  FILES = 'Files',
}

export type IOption = {
  label: string;
  value: any;
} & {
  [key: string]: any; // Các trường khác tùy ý
};

export type TSexOptionValue = {
  value: string;
  label: string;
};

export const ESexOptions: TSexOptionValue[] = [
  {
    value: 'nữ',
    label: 'Nữ',
  },
  {
    value: 'nam',
    label: 'Nam',
  },
];

export const EDocumentType: IOption[] = [
  {label: 'Căn cước', value: '<PERSON>ăn cước'},
];

const quocGia = [
  'Việt Nam',
  '<PERSON><PERSON>-<PERSON> (Sri Lankan)',
  'U-ru-goay (Uruguayan)',
  'Ix-ra-en (Israeli)',
  'Ja-mai-ca (Jamaican)',
  'Kê-ni-a (Kenyan)',
  'Cô-oét (Kuwaiti)',
  'Lê-xô-thô (Lesotho)',
  'Lúc-xăm-bua (Luxembourg)',
  'Ma-lai-xi-a (Malaysian)',
  'Pê-ru (Peruvian)',
  'Hàn Quốc (South Korean)',
  'An-ba-ni (Albanian)',
  'A-déc-bai-gian (Azerbaijani)',
  'Bê-la-rút (Belarusian)',
  'Bê-li-xê (Belizean)',
  'Bô-li-vi-a (Bolivian)',
  'En Xan-va-đo (EL Salvadorian)',
  'Ê-ri-tơ-ri-a (Eritrean)',
  'Phi-gi (Fijian)',
  'Hy Lạp (Greek)',
  'Dăm-bi-a (Zambian)',
  'Xinh-ga-po (Singaporean)',
  'Xu-đăng (Sudanese)',
  'Xu-ri-nam (Surinamese)',
  'Thái Lan (Thai)',
  'U-crai-na (Ukrainian)',
  'Hung-ga-ry (Hungarian)',
  'Nhật Bản (Japanese)',
  'Man-ta (Maltese)',
  'Mô-ri-ta-ni (Mauritanian)',
  'Mô-na-cô (Monaco)',
  'Ma-rốc (Moroccan)',
  'Nam-mi-bi-a (Namibian)',
  'Ni-giê (Nigerian)',
  'Pa-pu-a Niu Ghi-nê (Papua New Guinean)',
  'Pa-ra-goay (Paraguayan)',
  'Bun-ga-ri (Bulgarian)',
  'Crô-a-ti-a (Croatian)',
  'Áp-ganitxtan',
  'Đông Ti-mo (East Timorese)',
  'Ê-xtô-ni-a (Estonian)',
  'Ga-bông (Gabonese)',
  'Đức (German)',
  'Ghi-nê (Guinean)',
  'Dim-ba-bu-ê (Zimbabwean)',
  'Xê-nê-gan (Senegalese)',
  'Si-ê-ra Lê-ôn (Sierra Leonean)',
  'Thụy Điển (Swedish)',
  'Trung Quốc (Hồng Công) (Hong Kong, Chinese)',
  'Lào (Laotian)',
  'Quần đảo Mác-san (Marshallese Islands)',
  'Na-u-ru (Nauruan)',
  'Hà Lan (Dutch)',
  'Pa-na-ma (Panamanian)',
  'Ca-ta (Qatari)',
  'Ô-xtrây-li-a (Australian)',
  'Bu-run-đi (Burundian)',
  'Ca-mơ-run (Cameroonian)',
  'Triều Tiên (North Korean)',
  'Đô-mi-ni-ca (Dominicain)',
  'Goa-tê-ma-la (Guatemalan)',
  'Ru-ma-ni (Romanian)',
  'Xây-sen (Seychellois)',
  'Tây Ban Nha (Spanish)',
  'Thụy Sĩ (Swiss)',
  'Tô-gô (Togolese)',
  'Tuy-ni-di (Tunisian)',
  'Tuốc-mê-ni-xtan (Turkmen(s))',
  'U-dơ-bê-ki-xtan (Uzbekistani)',
  'Va-nu-a-tu (Ni-Vanuatu)',
  'Vê-nê-du-ê-la (Venezuelan)',
  'Guy-a-na (Guyanese)',
  'Ấn Độ (Indian)',
  'Lít-va (Lithuanian)',
  'Mi-an-ma (Burmese)',
  'Niu di-lân (New Zealand)',
  'Na Uy (Norwegian)',
  'Pa-lau (Palauan)',
  'Ba Lan (Polish)',
  'Ghi-nê Bít-xao (Guinea-Bissauan)',
  'Ăng-gô-la (Angolan)',
  'Bê-nanh (Beninese)',
  'Bô-xni-a Héc-dê-gô-vi-na (Bosnian, Herzegovinian)',
  'Găm-bi-a (Gambian)',
  'Xanh Vin-xen và Grê-na-đin (Saint Vincent and the Grenadines)',
  'Xy-ri (Syrian)',
  'Ma-xê-đô-ni-a (Macedonian)',
  'A-rập thống nhất (Emirian)',
  'Ai len (Irish)',
  'Lát-vi-a (Latvian)',
  'Mê-hi-cô (Mexican)',
  'Mông Cổ (Mongolian)',
  'Mô-dăm-bích (Mozambican)',
  'Ni-ca-ra-goa (Nicaraguan)',
  'Bồ Đào Nha (Portuguese)',
  'An-giê-ri (Algerian)',
  'Bác-ba-đốt (Barbadian)',
  'Buốc-ki-na Pha-xô (Burkinabe)',
  'Ca-na-đa (Canadian)',
  'Sát (Chadian)',
  'Công-gô (Congolese)',
  'Cu-ba (Cuban)',
  'Síp (Cypriot)',
  'Séc (Czech)',
  'Đô-mi-ni-ca-na (Dominican)',
  'Ê-ti-ô-pi-a (Ethiopian)',
  'Grê-na-đa (Grenadian)',
  'Nga (Russian)',
  'Ru-an-đa (Rwandan)',
  'Xa-moa (Samoan)',
  'Xlô-va-ki-a (Slovak / Slovakian)',
  'Xô-lô-môn (Solomon Islander)',
  'Tan-da-ni-a (Tanzanian)',
  'Thổ Nhĩ Kỳ (Turkish)',
  'Y-ê-men (Yemeni / Yemenite)',
  'Ai-xơ-len (Icelandic)',
  'I-rắc (Iraqi)',
  'Li-băng (Lebanese)',
  'Mô-ri-xơ (Mauritian)',
  'Mi-crô-nê-si-a (Micronesian)',
  'Ô-man (Omani)',
  'Bỉ (Belgian)',
  'Căm-pu-chia (Cambodian)',
  'Trung Phi (Middle African)',
  'Cốt-đi-voa (Ivorian)',
  'Ai Cập (Egyptian)',
  'Ghi-nê Xích đạo (Equatorial Guinean)',
  'Xéc-bi-a (Serbian)',
  'Người không quốc tịch (Stateless person)',
  'Xlô-ven-ni-a (Slovenian / Slovene)',
  'Nam Phi (South African)',
  'Trung Quốc (Đài Loan) (Taiwanese)',
  'Tát-gi-ki-xtan (Tajik)',
  'In-đô-nê-xi-a (Indonesian)',
  'I-ta-li-a (Italian)',
  'Ka-dắc-xtan (Kazakh / Kazakhstani)',
  'Trung Quốc (Ma-cao) (Macau, Chinese)',
  'Ma-đa-gát-xca (Malagasy)',
  'Man-đi-vơ (Maldivan)',
  'Ma-li (Malian)',
  'Môn-tê-nê-grô (Montenegrin)',
  'Pa-le-xtin (Palestinian)',
  'Pa-ki-xtan (Pakistani)',
  'Phi-líp-pin (Filipino)',
  'Băng-la-đét (Bangladeshi)',
  'Bốt-xoa-na (Botswanan)',
  'Bra-xin (Brazilian)',
  'Cáp-ve (Cape Verdian)',
  'Cô-lôm-bi-a (Colombian)',
  'Đan Mạch (Danish)',
  'Anh (British)',
  'Phần Lan (Finnish)',
  'Hoa Kỳ (American)',
];

export const ENationalityOptions: IOption[] = quocGia.map(item => ({
  label: item,
  value: item,
}));

const danTocVietNam = [
  'Kinh',
  'Tày',
  'Thái',
  'Hoa',
  'Khơ-me',
  'Mường',
  'Nùng',
  'HMông',
  'Dao',
  'Gia-rai',
  'Ngái',
  'Ê-đê',
  'Ba na',
  'Xơ Đăng',
  'Sán Chay',
  'Cơ-ho',
  'Chăm',
  'Sán Dìu',
  'Hrê',
  'Mnông',
  'Ra-glai',
  'Xtiêng',
  'Bru-Vân Kiều',
  'Thổ',
  'Giáy',
  'Cơ-tu',
  'Gié Triêng',
  'Mạ',
  'Khơ-mú',
  'Co',
  'Tà-ôi',
  'Chơ-ro',
  'Kháng',
  'Xinh-mun',
  'Hà Nhì',
  'Chu-ru',
  'Lào',
  'La Chí',
  'Phù Lá',
  'La Hủ',
  'La Ha',
  'Pà Thẻn',
  'Lự',
  'Người Chứt',
  'Lô Lô',
  'Mảng',
  'Cờ Lao',
  'Bố Y',
  'Cống',
  'Si La',
  'Pu Péo',
  'Rơ-măm',
  'Brâu',
  'Ơ Đu',
  'Rơ măm',
  'Người nước ngoài',
  'Không rõ',
];

export const EDTOptions: IOption[] = danTocVietNam.map(item => ({
  label: item,
  value: item,
}));

export const ETGOptions: IOption[] = [
  {label: 'Không', value: 'Không'},
  {label: 'Phật giáo', value: 'Phật giáo'},
  {label: 'Công giáo', value: 'Công giáo'},
  {label: 'Tin lành', value: 'Tin lành'},
  {label: 'Cao Đài', value: 'Cao Đài'},
  {label: 'Phật giáo Hòa Hảo', value: 'Phật giáo Hòa Hảo'},
  {label: 'Hồi giáo', value: 'Hồi giáo'},
  {label: "Tôn giáo Baha'i", value: "Tôn giáo Baha'i"},
  {
    label: 'Tịnh độ Cư sỹ Phật hội Việt Nam',
    value: 'Tịnh độ Cư sỹ Phật hội Việt Nam',
  },
  {label: 'Đạo Tứ Ân Hiếu nghĩa', value: 'Đạo Tứ Ân Hiếu nghĩa'},
  {label: 'Bửu Sơn Kỳ hương', value: 'Bửu Sơn Kỳ hương'},
  {
    label: 'Giáo hội Phật đường Nam Tông Minh Sư đạo',
    value: 'Giáo hội Phật đường Nam Tông Minh Sư đạo',
  },
  {
    label: 'Hội thánh Minh lý đạo - Tam Tông Miếu',
    value: 'Hội thánh Minh lý đạo - Tam Tông Miếu',
  },
  {label: 'Chăm Bà la môn', value: 'Chăm Bà la môn'},
  {
    label: 'Giáo hội Các thánh hữu Ngày sau của Chúa Giê su Ky tô (Mormon)',
    value: 'Giáo hội Các thánh hữu Ngày sau của Chúa Giê su Ky tô (Mormon)',
  },
  {label: 'Phật giáo Hiếu Nghĩa Tà Lơn', value: 'Phật giáo Hiếu Nghĩa Tà Lơn'},
  {
    label: 'Giáo hội Cơ đốc Phục lâm Việt Nam',
    value: 'Giáo hội Cơ đốc Phục lâm Việt Nam',
  },
];

export const ENcOptions: IOption[] = [
  {
    label: 'Cục cảnh sát đăng ký quản lý cư trú và dữ liệu quốc gia về dân cư',
    value: 'Cục cảnh sát đăng ký quản lý cư trú và dữ liệu quốc gia về dân cư',
  },
  {
    label: 'Cục cảnh sát quản lý hành chính về trật tự xã hội',
    value: 'Cục cảnh sát quản lý hành chính về trật tự xã hội',
  },
  {label: 'Bộ công an', value: 'Bộ công an'},
  {label: 'Cục quản lý xuất nhập cảnh', value: 'Cục quản lý xuất nhập cảnh'},
];

export type CitizenCardKey =
  | 'FullName'
  | 'DateOfBirth'
  | 'Sex'
  | 'CitizenCode'
  | 'DateOfProvision'
  | 'PlaceOfIssues'
  | 'Nationality'
  | 'PlaceOfResidence'
  | 'DocumentType'
  | 'Nation'
  | 'Religion';

export type CitizenCardOptionalDefaultArrayItem = {
  key: CitizenCardKey;
  label: string;
};
export const CitizenCardOptionalDefault: CitizenCardOptionalDefaultArrayItem[] =
  [
    {key: 'FullName', label: 'Họ và tên'},
    {key: 'DateOfBirth', label: 'Ngày, tháng, năm sinh'},
    {key: 'Sex', label: 'Giới tính'},
    {key: 'CitizenCode', label: 'Số CCCD'},
    {key: 'DateOfProvision', label: 'Ngày cấp'},
    {key: 'PlaceOfIssues', label: 'Nơi cấp'},
    {key: 'Nationality', label: 'Quốc tịch'},
    {key: 'PlaceOfResidence', label: 'Nơi thường trú'},
    {key: 'DocumentType', label: 'Loại giấy tờ'},
    {key: 'Nation', label: 'Dân tộc'},
    {key: 'Religion', label: 'Tôn giáo'},
  ];

export const CitizenCardRequired = [
  'FullName',
  'DateOfBirth',
  'CitizenCode',
  'DateOfProvision',
  'PlaceOfIssues',
  'PlaceOfResidence',
  'PlaceOfOrigin',
];

export const defaultDataType = ['email', 'phone', 'text'];

export const BtsSelect = [...Object.values(EBtsInformationUserOptions)];

export const IDoiTuongThucHienOptions: IOption[] = [
  {label: 'Bản thân', value: 'null'},
  {label: 'Uỷ quyền cá nhân', value: 'canhan'},
  {label: 'Uỷ quyền doanh nghiệp', value: 'doanhnghiep'},
];

export type IDoiTuongThucHienOptionValue = {
  doiTuongThucHien: IOption;
};
