import {RequesterApi} from '../requester-client';

const getRequesterServiceByPartnerCode = (partnerCode: string) => {
  return RequesterApi.get(
    `/v1/dvc-ha-noi/requester-service-by-partner-code/${partnerCode}`,
  );
};

const submitDocumentInstant = ({
  shareDocumentId,
}: {
  shareDocumentId: string;
}) => {
  console.log('Submitting document with shareDocumentId:', shareDocumentId);
  return RequesterApi.post(`/v1/dvc-ha-noi/submit/${shareDocumentId}`);
};

const getPaymentInfoDetail = async ({
  hoSoId,
  keyEformId,
  donViId,
  linhVucId,
  hoSoKemTheoId,
  thuTucHanhChinhId,
}: {
  HoSoID: string;
  KeyEformID: string;
  DonViID: string;
  LinhVucID: string;
  HoSoKemTheoID?: string;
  ThuTucHanhChinhID: string;
}) => {
  console.log('Fetching payment info detail with params:', {
    hoSoId,
    keyEformId,
    donViId,
    linhVucId,
    hoSoKemTheoId,
    thuTucHanhChinhId,
  });
  const result = await RequesterApi.get(
    '/v1/dvc-ha-noi/payment/thong-tin-thanh-toan',
    {
      params: {
        HoSoID: hoSoId,
        KeyEformID: keyEformId,
        DonViID: donViId,
        LinhVucID: linhVucId,
        HoSoKemTheoID: hoSoKemTheoId || '0',
        ThuTucHanhChinhID: thuTucHanhChinhId,
      },
    },
  );
  console.log('getPaymentInfoDetail result:', result);
  return result;
};

export const dvcQuangNinh = {
  getRequesterServiceByPartnerCode,
  submitDocumentInstant,
  getPaymentInfoDetail,
};
